package com.bzlj.craft.transform.common;

import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.producer.MessageEventProducer;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.core.utils.SnowflakeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
@Slf4j
public abstract class AbsCommonConsumer {
    @Autowired
    private MessageEventProducer messageEventProducer;

    /**
     * 公共的数据处理逻辑
     *
     * @param messageInfo
     * @param modelName
     * <AUTHOR>
     * @date 2025/5/29 17:44
     */
    public void handleData(ForwardMessageInfo<?> messageInfo, String modelName) {
        try {
            MessageEvent event = new MessageEvent();
            event.setMessageId(messageInfo.getTelegramId());
            event.setMessageType(messageInfo.getServiceId());
            event.setPayload(messageInfo.getPayload());
            messageEventProducer.pushMessage(event);
        } catch (Exception e) {
            log.error("消费[{}]kafka数据失败:topic:{},电文内容:{}", modelName,
                    messageInfo.getServiceId(),
                    JsonUtils.toJson(messageInfo),
                    e);
            throw new RuntimeException(e);
        }
    }
}
