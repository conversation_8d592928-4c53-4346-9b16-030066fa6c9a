package com.bzlj.craft.transform.common;

import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.handler.IMessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;

/**
 * 通用消息处理器抽象类
 * <p>
 * 该类提供了消息处理的通用框架，包括消息处理前后的统一逻辑处理，
 * 如日志记录、耗时统计、历史数据清理等功能。
 * 具体的业务逻辑需要子类实现相应的抽象方法。
 * </p>
 *
 * @param <T> 消息载荷类型
 * <AUTHOR>
 * @date 2025-06-06 10:42
 */
@Slf4j
public abstract class CommonHandler<T> implements IMessageHandler<T> {

    /**
     * 处理器描述信息，用于日志输出
     */
    public String desc;

    /**
     * UTC时区偏移量，用于时间计算
     */
    final ZoneOffset offset = ZoneOffset.UTC;

    /**
     * 转换处理方法，子类需要实现具体的业务逻辑
     *
     * @param t 待处理的消息载荷
     */
    public abstract void transform(T t, Map<String, Object> handleContext);

    /**
     * 处理历史遗留数据
     *
     * @param relationIds 关联ID列表
     */
    public abstract void dealLegacyData(List<String> relationIds, Map<String, Object> handleContext);

    /**
     * 获取关联ID列表
     *
     * @param t 消息载荷
     * @return 关联ID列表
     */
    public abstract List<String> getRelationIds(T t);

    /**
     * 清理关联数据
     *
     * @param telegramId 电报ID
     */
    public abstract void clearRelationData(String telegramId);
    /**
     * 消息处理前
     *
     * @param event         消息事件
     * @param handleContext 处理上下文
     */
    @Override
    public void handlePre(MessageEvent<T> event, Map<String, Object> handleContext, Map<String, Object> handlesContext) {
        handleContext.put("startTime", LocalDateTime.now());
        handleContext.put("messageId", event.getMessageId());
        log.info("{}消息{}开始处理", getImplDesc(event.getMessageType()), event.getMessageId());
    }

    /**
     * 消息处理核心方法
     * <p>
     * 调用具体的转换方法处理消息载荷
     * </p>
     *
     * @param messageEvent 消息事件
     * @param map 处理上下文
     * @param map1 处理器上下文
     */
    @Override
    public void handle(MessageEvent<T> messageEvent, Map<String, Object> map, Map<String, Object> map1) {
        transform(messageEvent.getPayload(),map);
    }

    /**
     * 消息处理后置方法
     * <p>
     * 在消息处理完成后执行，主要功能包括：
     * 1. 计算处理耗时
     * 2. 处理历史遗留数据
     * 3. 清理关联数据
     * 4. 记录处理完成日志
     * </p>
     *
     * @param event 消息事件
     * @param currentHandleContext 当前处理上下文
     * @param handlesContext 处理器上下文
     */
    @Override
    public void handlePost(MessageEvent<T> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext){
        LocalDateTime startTime = (LocalDateTime) currentHandleContext.get("startTime");
        long startMillis = startTime.atOffset(offset).toInstant().toEpochMilli();
        long endMillis = LocalDateTime.now().atOffset(offset).toInstant().toEpochMilli();

        try {
            // 处理历史遗留数据 - 在独立事务中执行
            dealLegacyData(getRelationIds(event.getPayload()),currentHandleContext);
            // 清理关联数据 - 在独立事务中执行
            clearRelationData(event.getMessageId());
        } catch (Exception e) {
            log.error("处理后置操作失败: {}", e.getMessage(), e);
            // 后置操作失败不应该影响主业务流程，只记录日志
        }

        log.info("{}消息{}处理完成,耗时:{}ms", getImplDesc(event.getMessageType()),
                event.getMessageId(), endMillis - startMillis);
    }

    /**
     * 获取实现类描述信息
     * <p>
     * 如果描述信息已存在则直接返回，否则通过消息类型获取描述信息并缓存
     * </p>
     *
     * @param messageType 消息类型
     * @return 实现类描述信息
     */
    public String getImplDesc(String messageType) {
        if (StringUtils.isNotBlank(desc)) {
            return desc;
        }
        this.desc = getDesc(messageType);
        return desc;
    }

    /**
     * 错误处理
     *
     * @param event         错误消息事件
     * @param currentHandleContext 处理上下文
     * @param e             错误信息
     */
    @Override
    public void handleError(MessageEvent<T> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext, Exception e) {
        log.error("{}消息{}处理异常:{}", getImplDesc(event.getMessageType()), event.getMessageId(), e.getMessage(), e);
    }


}
